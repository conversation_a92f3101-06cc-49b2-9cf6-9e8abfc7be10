import 'package:cloud_firestore/cloud_firestore.dart';

class DeviceModel {
  final String id;
  final String name;
  final String userId;
  final String userEmail;
  final bool isOnline;
  final bool relayState;
  final double tdsLevel;
  final double flowRate;
  final double temperature;
  final double pressure;
  final DateTime lastSeen;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic> sensorData;
  final String location;
  final String firmwareVersion;

  DeviceModel({
    required this.id,
    required this.name,
    required this.userId,
    required this.userEmail,
    this.isOnline = false,
    this.relayState = false,
    this.tdsLevel = 0.0,
    this.flowRate = 0.0,
    this.temperature = 0.0,
    this.pressure = 0.0,
    required this.lastSeen,
    required this.createdAt,
    required this.updatedAt,
    this.sensorData = const {},
    this.location = '',
    this.firmwareVersion = '',
  });

  factory DeviceModel.fromMap(Map<String, dynamic> map, String documentId) {
    return DeviceModel(
      id: documentId,
      name: map['name'] ?? 'Unknown Device',
      userId: map['userId'] ?? '',
      userEmail: map['userEmail'] ?? '',
      isOnline: map['isOnline'] ?? false,
      relayState: map['relayState'] ?? false,
      tdsLevel: (map['tdsLevel'] ?? 0.0).toDouble(),
      flowRate: (map['flowRate'] ?? 0.0).toDouble(),
      temperature: (map['temperature'] ?? 0.0).toDouble(),
      pressure: (map['pressure'] ?? 0.0).toDouble(),
      lastSeen: map['lastSeen']?.toDate() ?? DateTime.now(),
      createdAt: map['createdAt']?.toDate() ?? DateTime.now(),
      updatedAt: map['updatedAt']?.toDate() ?? DateTime.now(),
      sensorData: Map<String, dynamic>.from(map['sensorData'] ?? {}),
      location: map['location'] ?? '',
      firmwareVersion: map['firmwareVersion'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'userId': userId,
      'userEmail': userEmail,
      'isOnline': isOnline,
      'relayState': relayState,
      'tdsLevel': tdsLevel,
      'flowRate': flowRate,
      'temperature': temperature,
      'pressure': pressure,
      'lastSeen': Timestamp.fromDate(lastSeen),
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'sensorData': sensorData,
      'location': location,
      'firmwareVersion': firmwareVersion,
    };
  }

  DeviceModel copyWith({
    String? id,
    String? name,
    String? userId,
    String? userEmail,
    bool? isOnline,
    bool? relayState,
    double? tdsLevel,
    double? flowRate,
    double? temperature,
    double? pressure,
    DateTime? lastSeen,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? sensorData,
    String? location,
    String? firmwareVersion,
  }) {
    return DeviceModel(
      id: id ?? this.id,
      name: name ?? this.name,
      userId: userId ?? this.userId,
      userEmail: userEmail ?? this.userEmail,
      isOnline: isOnline ?? this.isOnline,
      relayState: relayState ?? this.relayState,
      tdsLevel: tdsLevel ?? this.tdsLevel,
      flowRate: flowRate ?? this.flowRate,
      temperature: temperature ?? this.temperature,
      pressure: pressure ?? this.pressure,
      lastSeen: lastSeen ?? this.lastSeen,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      sensorData: sensorData ?? this.sensorData,
      location: location ?? this.location,
      firmwareVersion: firmwareVersion ?? this.firmwareVersion,
    );
  }

  String get statusText => isOnline ? 'Online' : 'Offline';
  String get relayStatusText => relayState ? 'ON' : 'OFF';
  String get tdsLevelText => '${tdsLevel.toStringAsFixed(1)} ppm';
  String get flowRateText => '${flowRate.toStringAsFixed(1)} L/min';
  String get temperatureText => '${temperature.toStringAsFixed(1)}°C';
  String get pressureText => '${pressure.toStringAsFixed(1)} bar';

  bool get isHealthy {
    if (!isOnline) return false;
    if (tdsLevel > 500) return false; // High TDS indicates poor water quality
    if (temperature > 40 || temperature < 5) return false; // Temperature out of range
    return true;
  }

  @override
  String toString() {
    return 'DeviceModel(id: $id, name: $name, isOnline: $isOnline, tdsLevel: $tdsLevel)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DeviceModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
