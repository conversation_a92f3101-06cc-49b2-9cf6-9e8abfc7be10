import 'dart:async';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'firestore_service.dart';

class FCMService {
  static final FCMService _instance = FCMService._internal();
  factory FCMService() => _instance;
  FCMService._internal();

  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  final FirestoreService _firestoreService = FirestoreService();
  
  String? _currentToken;
  StreamSubscription<String>? _tokenSubscription;
  
  String? get currentToken => _currentToken;

  /// Initialize FCM service
  Future<void> initialize() async {
    try {
      // Request notification permissions
      await _requestPermissions();
      
      // Get initial token
      await _getAndStoreToken();
      
      // Listen for token refresh
      _listenForTokenRefresh();
      
      // Configure message handlers
      _configureMessageHandlers();
      
    } catch (e) {
      debugPrint('FCM initialization failed: $e');
    }
  }

  /// Request notification permissions
  Future<bool> _requestPermissions() async {
    try {
      // Request FCM permissions
      final settings = await _messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      // Request system notification permissions
      final status = await Permission.notification.request();
      
      final fcmGranted = settings.authorizationStatus == AuthorizationStatus.authorized;
      final systemGranted = status == PermissionStatus.granted;
      
      debugPrint('FCM permission: $fcmGranted, System permission: $systemGranted');
      return fcmGranted && systemGranted;
      
    } catch (e) {
      debugPrint('Permission request failed: $e');
      return false;
    }
  }

  /// Get FCM token and store it
  Future<String?> _getAndStoreToken() async {
    try {
      final token = await _messaging.getToken();
      if (token != null) {
        _currentToken = token;
        await _storeTokenLocally(token);
        debugPrint('FCM Token obtained: ${token.substring(0, 20)}...');
        return token;
      }
    } catch (e) {
      debugPrint('Failed to get FCM token: $e');
    }
    return null;
  }

  /// Store token locally for offline access
  Future<void> _storeTokenLocally(String token) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('fcm_token', token);
    } catch (e) {
      debugPrint('Failed to store FCM token locally: $e');
    }
  }

  /// Get stored token from local storage
  Future<String?> getStoredToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('fcm_token');
    } catch (e) {
      debugPrint('Failed to get stored FCM token: $e');
      return null;
    }
  }

  /// Listen for token refresh
  void _listenForTokenRefresh() {
    _tokenSubscription = _messaging.onTokenRefresh.listen(
      (newToken) async {
        _currentToken = newToken;
        await _storeTokenLocally(newToken);
        await updateUserToken(newToken);
        debugPrint('FCM Token refreshed: ${newToken.substring(0, 20)}...');
      },
      onError: (error) {
        debugPrint('FCM token refresh error: $error');
      },
    );
  }

  /// Update user's FCM token in Firestore
  Future<void> updateUserToken(String token, {String? userId}) async {
    try {
      await _firestoreService.updateUserFCMToken(userId, token);
      debugPrint('User FCM token updated in Firestore');
    } catch (e) {
      debugPrint('Failed to update user FCM token: $e');
    }
  }

  /// Configure message handlers
  void _configureMessageHandlers() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      debugPrint('Foreground message received: ${message.notification?.title}');
      _handleForegroundMessage(message);
    });

    // Handle background message taps
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      debugPrint('Background message tapped: ${message.notification?.title}');
      _handleMessageTap(message);
    });

    // Handle app launch from terminated state
    _handleAppLaunchFromMessage();
  }

  /// Handle foreground messages
  void _handleForegroundMessage(RemoteMessage message) {
    // You can show local notifications here or update UI
    // For now, just log the message
    debugPrint('Foreground message data: ${message.data}');
  }

  /// Handle message tap (background/terminated)
  void _handleMessageTap(RemoteMessage message) {
    // Navigate to specific screen based on message data
    final data = message.data;
    debugPrint('Message tap data: $data');
    
    // Example: Navigate to device screen if device-related notification
    if (data.containsKey('deviceId')) {
      // Navigate to device details
      debugPrint('Navigate to device: ${data['deviceId']}');
    }
  }

  /// Handle app launch from terminated state
  Future<void> _handleAppLaunchFromMessage() async {
    final initialMessage = await _messaging.getInitialMessage();
    if (initialMessage != null) {
      debugPrint('App launched from message: ${initialMessage.notification?.title}');
      _handleMessageTap(initialMessage);
    }
  }

  /// Send device notification
  Future<void> sendDeviceNotification({
    required String userId,
    required String deviceId,
    required String title,
    required String body,
    Map<String, String>? data,
  }) async {
    try {
      // This would typically be done from your backend
      // For now, we'll just log the notification details
      debugPrint('Device notification: $title - $body');
      debugPrint('Target user: $userId, Device: $deviceId');
      debugPrint('Data: $data');
      
      // In a real implementation, you would call your backend API
      // to send the notification using the FCM Admin SDK
      
    } catch (e) {
      debugPrint('Failed to send device notification: $e');
    }
  }

  /// Subscribe to device-specific topic
  Future<void> subscribeToDeviceTopic(String deviceId) async {
    try {
      final topic = 'device_$deviceId';
      await _messaging.subscribeToTopic(topic);
      debugPrint('Subscribed to device topic: $topic');
    } catch (e) {
      debugPrint('Failed to subscribe to device topic: $e');
    }
  }

  /// Unsubscribe from device-specific topic
  Future<void> unsubscribeFromDeviceTopic(String deviceId) async {
    try {
      final topic = 'device_$deviceId';
      await _messaging.unsubscribeFromTopic(topic);
      debugPrint('Unsubscribed from device topic: $topic');
    } catch (e) {
      debugPrint('Failed to unsubscribe from device topic: $e');
    }
  }

  /// Subscribe to user-specific notifications
  Future<void> subscribeToUserNotifications(String userId) async {
    try {
      await _messaging.subscribeToTopic('user_$userId');
      await _messaging.subscribeToTopic('all_users');
      debugPrint('Subscribed to user notifications: $userId');
    } catch (e) {
      debugPrint('Failed to subscribe to user notifications: $e');
    }
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    try {
      final settings = await _messaging.getNotificationSettings();
      return settings.authorizationStatus == AuthorizationStatus.authorized;
    } catch (e) {
      debugPrint('Failed to check notification status: $e');
      return false;
    }
  }

  /// Get notification settings
  Future<NotificationSettings> getNotificationSettings() async {
    return await _messaging.getNotificationSettings();
  }

  /// Dispose resources
  void dispose() {
    _tokenSubscription?.cancel();
  }
}

/// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  debugPrint('Background message received: ${message.notification?.title}');
  // Handle background message processing here
}
