import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../colors.dart';
import '../services/device_registration_service.dart';
import '../services/wifi_config_service.dart';
import '../constance.dart';

class DeviceSetupWizard extends StatefulWidget {
  const DeviceSetupWizard({super.key});

  @override
  State<DeviceSetupWizard> createState() => _DeviceSetupWizardState();
}

class _DeviceSetupWizardState extends State<DeviceSetupWizard> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  final int _totalSteps = 4;

  // Form data
  final _deviceNameController = TextEditingController();
  final _locationController = TextEditingController();
  String? _selectedSSID;
  final _passwordController = TextEditingController();
  String? _generatedDeviceId;

  @override
  void initState() {
    super.initState();
    _generatedDeviceId = context.read<DeviceRegistrationService>().generateDeviceId();
  }

  @override
  void dispose() {
    _deviceNameController.dispose();
    _locationController.dispose();
    _passwordController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text('Device Setup (${_currentStep + 1}/$_totalSteps)'),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => _handleBackPress(),
        ),
      ),
      body: Column(
        children: [
          _buildProgressIndicator(),
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildDeviceInfoStep(),
                _buildWiFiSelectionStep(),
                _buildWiFiPasswordStep(),
                _buildConfirmationStep(),
              ],
            ),
          ),
          _buildNavigationButtons(),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: List.generate(_totalSteps, (index) {
          final isCompleted = index < _currentStep;
          final isCurrent = index == _currentStep;
          
          return Expanded(
            child: Container(
              margin: EdgeInsets.only(right: index < _totalSteps - 1 ? 8 : 0),
              height: 4,
              decoration: BoxDecoration(
                color: isCompleted || isCurrent 
                    ? AppColors.accent 
                    : Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildDeviceInfoStep() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Device Information',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Give your device a name and location to identify it easily.',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 32),
          _buildInfoCard(
            'Device ID',
            _generatedDeviceId ?? 'Generating...',
            Icons.fingerprint,
          ),
          const SizedBox(height: 24),
          TextField(
            controller: _deviceNameController,
            decoration: InputDecoration(
              labelText: 'Device Name',
              hintText: 'e.g., Kitchen Water Monitor',
              prefixIcon: const Icon(Icons.device_hub),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _locationController,
            decoration: InputDecoration(
              labelText: 'Location',
              hintText: 'e.g., Kitchen, Bathroom, Garden',
              prefixIcon: const Icon(Icons.location_on),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWiFiSelectionStep() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Select WiFi Network',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Choose the WiFi network your device will connect to.',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _scanWiFiNetworks(),
                  icon: const Icon(Icons.refresh),
                  label: const Text('Scan Networks'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.accent,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Consumer<WiFiConfigService>(
              builder: (context, wifiService, child) {
                if (wifiService.status == WiFiConfigStatus.scanning) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Scanning for networks...'),
                      ],
                    ),
                  );
                }

                if (wifiService.availableNetworks.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.wifi_off, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text('No networks found'),
                        Text('Tap "Scan Networks" to search'),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  itemCount: wifiService.availableNetworks.length,
                  itemBuilder: (context, index) {
                    final network = wifiService.availableNetworks[index];
                    final isSelected = _selectedSSID == network.ssid;

                    return Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      decoration: BoxDecoration(
                        color: isSelected 
                            ? AppColors.accent.withOpacity(0.1)
                            : AppColors.cardBackground,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected 
                              ? AppColors.accent 
                              : AppColors.borderColor,
                        ),
                      ),
                      child: ListTile(
                        leading: Icon(
                          network.isSecure ? Icons.wifi_lock : Icons.wifi,
                          color: _getSignalColor(network.signalStrength),
                        ),
                        title: Text(
                          network.ssid,
                          style: TextStyle(
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                        subtitle: Text(
                          '${network.signalStrengthText} • ${network.isSecure ? 'Secured' : 'Open'}',
                        ),
                        trailing: isSelected 
                            ? Icon(Icons.check_circle, color: AppColors.accent)
                            : null,
                        onTap: () {
                          setState(() {
                            _selectedSSID = network.ssid;
                          });
                        },
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWiFiPasswordStep() {
    final selectedNetwork = context.read<WiFiConfigService>()
        .availableNetworks
        .where((n) => n.ssid == _selectedSSID)
        .firstOrNull;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'WiFi Password',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Enter the password for "${_selectedSSID ?? 'selected network'}".',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 32),
          if (selectedNetwork != null) ...[
            _buildInfoCard(
              'Network',
              selectedNetwork.ssid,
              selectedNetwork.isSecure ? Icons.wifi_lock : Icons.wifi,
            ),
            const SizedBox(height: 24),
          ],
          if (selectedNetwork?.isSecure == true)
            TextField(
              controller: _passwordController,
              obscureText: true,
              decoration: InputDecoration(
                labelText: 'WiFi Password',
                hintText: 'Enter network password',
                prefixIcon: const Icon(Icons.lock),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            )
          else
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info, color: Colors.green),
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'This is an open network. No password required.',
                      style: TextStyle(color: Colors.green),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildConfirmationStep() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Confirm Setup',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Review your device configuration before proceeding.',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 32),
          _buildInfoCard('Device ID', _generatedDeviceId ?? '', Icons.fingerprint),
          const SizedBox(height: 16),
          _buildInfoCard('Device Name', _deviceNameController.text, Icons.device_hub),
          const SizedBox(height: 16),
          _buildInfoCard('Location', _locationController.text, Icons.location_on),
          const SizedBox(height: 16),
          _buildInfoCard('WiFi Network', _selectedSSID ?? '', Icons.wifi),
          const SizedBox(height: 16),
          _buildInfoCard('MQTT Broker', AppConstants.mqttBroker, Icons.cloud),
          const SizedBox(height: 32),
          Consumer<DeviceRegistrationService>(
            builder: (context, deviceService, child) {
              if (deviceService.status == DeviceRegistrationStatus.registering) {
                return Column(
                  children: [
                    LinearProgressIndicator(
                      value: deviceService.progress,
                      backgroundColor: Colors.grey[300],
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.accent),
                    ),
                    const SizedBox(height: 16),
                    const Text('Registering device...'),
                  ],
                );
              }

              if (deviceService.status == DeviceRegistrationStatus.completed) {
                return Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green),
                      SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Device registered successfully!',
                          style: TextStyle(
                            color: Colors.green,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }

              if (deviceService.status == DeviceRegistrationStatus.error) {
                return Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.error, color: Colors.red),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          deviceService.errorMessage ?? 'Registration failed',
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                    ],
                  ),
                );
              }

              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Row(
        children: [
          Icon(icon, color: AppColors.accent),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _goToPreviousStep,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text('Back'),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _canProceed() ? _goToNextStep : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.accent,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(_getNextButtonText()),
            ),
          ),
        ],
      ),
    );
  }

  Color _getSignalColor(int strength) {
    if (strength >= 75) return Colors.green;
    if (strength >= 50) return Colors.orange;
    return Colors.red;
  }

  bool _canProceed() {
    switch (_currentStep) {
      case 0:
        return _deviceNameController.text.trim().isNotEmpty &&
               _locationController.text.trim().isNotEmpty;
      case 1:
        return _selectedSSID != null;
      case 2:
        final selectedNetwork = context.read<WiFiConfigService>()
            .availableNetworks
            .where((n) => n.ssid == _selectedSSID)
            .firstOrNull;
        return selectedNetwork?.isSecure != true || 
               _passwordController.text.trim().isNotEmpty;
      case 3:
        return true;
      default:
        return false;
    }
  }

  String _getNextButtonText() {
    switch (_currentStep) {
      case 0:
        return 'Continue';
      case 1:
        return 'Continue';
      case 2:
        return 'Continue';
      case 3:
        return 'Register Device';
      default:
        return 'Next';
    }
  }

  void _handleBackPress() {
    if (_currentStep > 0) {
      _goToPreviousStep();
    } else {
      Navigator.of(context).pop();
    }
  }

  void _goToPreviousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToNextStep() {
    if (_currentStep < _totalSteps - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _registerDevice();
    }
  }

  Future<void> _scanWiFiNetworks() async {
    final wifiService = context.read<WiFiConfigService>();
    await wifiService.scanNetworks();
  }

  Future<void> _registerDevice() async {
    final deviceService = context.read<DeviceRegistrationService>();
    
    final result = await deviceService.registerDevice(
      deviceName: _deviceNameController.text.trim(),
      location: _locationController.text.trim(),
      customDeviceId: _generatedDeviceId,
    );

    if (result.success) {
      // Show success and navigate back
      await Future.delayed(const Duration(seconds: 2));
      if (mounted) {
        Navigator.of(context).popUntil((route) => route.isFirst);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Device "${_deviceNameController.text}" added successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }
}
