import 'package:cloud_firestore/cloud_firestore.dart';

class AnalyticsModel {
  final String id;
  final DateTime date;
  final double totalWaterUsage;
  final int activeDevices;
  final int totalUsers;
  final int activeUsers;
  final double revenue;
  final Map<String, int> planSubscriptions;
  final Map<String, double> deviceUsage;
  final Map<String, int> hourlyUsage;
  final List<Map<String, dynamic>> topUsers;
  final double averageTDS;
  final int systemAlerts;
  final DateTime createdAt;

  AnalyticsModel({
    required this.id,
    required this.date,
    this.totalWaterUsage = 0.0,
    this.activeDevices = 0,
    this.totalUsers = 0,
    this.activeUsers = 0,
    this.revenue = 0.0,
    this.planSubscriptions = const {},
    this.deviceUsage = const {},
    this.hourlyUsage = const {},
    this.topUsers = const [],
    this.averageTDS = 0.0,
    this.systemAlerts = 0,
    required this.createdAt,
  });

  factory AnalyticsModel.fromMap(Map<String, dynamic> map, String documentId) {
    return AnalyticsModel(
      id: documentId,
      date: map['date']?.toDate() ?? DateTime.now(),
      totalWaterUsage: (map['totalWaterUsage'] ?? 0.0).toDouble(),
      activeDevices: map['activeDevices'] ?? 0,
      totalUsers: map['totalUsers'] ?? 0,
      activeUsers: map['activeUsers'] ?? 0,
      revenue: (map['revenue'] ?? 0.0).toDouble(),
      planSubscriptions: Map<String, int>.from(map['planSubscriptions'] ?? {}),
      deviceUsage: Map<String, double>.from(map['deviceUsage'] ?? {}),
      hourlyUsage: Map<String, int>.from(map['hourlyUsage'] ?? {}),
      topUsers: List<Map<String, dynamic>>.from(map['topUsers'] ?? []),
      averageTDS: (map['averageTDS'] ?? 0.0).toDouble(),
      systemAlerts: map['systemAlerts'] ?? 0,
      createdAt: map['createdAt']?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'date': Timestamp.fromDate(date),
      'totalWaterUsage': totalWaterUsage,
      'activeDevices': activeDevices,
      'totalUsers': totalUsers,
      'activeUsers': activeUsers,
      'revenue': revenue,
      'planSubscriptions': planSubscriptions,
      'deviceUsage': deviceUsage,
      'hourlyUsage': hourlyUsage,
      'topUsers': topUsers,
      'averageTDS': averageTDS,
      'systemAlerts': systemAlerts,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  String get totalWaterUsageText => '${totalWaterUsage.toStringAsFixed(1)}L';
  String get revenueText => '\$${revenue.toStringAsFixed(2)}';
  String get averageTDSText => '${averageTDS.toStringAsFixed(1)} ppm';
  String get activeDevicesText => '$activeDevices devices';
  String get activeUsersText => '$activeUsers / $totalUsers users';

  double get userActivityPercentage {
    if (totalUsers == 0) return 0.0;
    return (activeUsers / totalUsers).clamp(0.0, 1.0);
  }

  @override
  String toString() {
    return 'AnalyticsModel(date: $date, totalWaterUsage: $totalWaterUsage, activeDevices: $activeDevices)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AnalyticsModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class SystemLogModel {
  final String id;
  final String level; // INFO, WARNING, ERROR, CRITICAL
  final String category; // DEVICE, USER, SYSTEM, PAYMENT, AUTH
  final String message;
  final String? deviceId;
  final String? userId;
  final Map<String, dynamic> metadata;
  final DateTime timestamp;

  SystemLogModel({
    required this.id,
    required this.level,
    required this.category,
    required this.message,
    this.deviceId,
    this.userId,
    this.metadata = const {},
    required this.timestamp,
  });

  factory SystemLogModel.fromMap(Map<String, dynamic> map, String documentId) {
    return SystemLogModel(
      id: documentId,
      level: map['level'] ?? 'INFO',
      category: map['category'] ?? 'SYSTEM',
      message: map['message'] ?? '',
      deviceId: map['deviceId'],
      userId: map['userId'],
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
      timestamp: map['timestamp']?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'level': level,
      'category': category,
      'message': message,
      'deviceId': deviceId,
      'userId': userId,
      'metadata': metadata,
      'timestamp': Timestamp.fromDate(timestamp),
    };
  }

  bool get isError => level == 'ERROR' || level == 'CRITICAL';
  bool get isWarning => level == 'WARNING';
  bool get isInfo => level == 'INFO';

  @override
  String toString() {
    return 'SystemLogModel(level: $level, category: $category, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SystemLogModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
