import 'dart:async';
import 'dart:convert';
import 'package:aqua5_app/constance.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';

enum MQTTConnectionState { disconnected, connecting, connected, error }

class MQTTService {
  late MqttServerClient client;
  final String clientId;
  Function(String, String)? onMessage;
  Function(String, Map<String, dynamic>)? onDeviceData;
  Function(MQTTConnectionState)? onConnectionStateChanged;

  MQTTConnectionState _connectionState = MQTTConnectionState.disconnected;
  Timer? _reconnectTimer;
  Timer? _heartbeatTimer;
  final Map<String, DateTime> _deviceLastSeen = {};
  final Map<String, Map<String, dynamic>> _deviceData = {};

  MQTTConnectionState get connectionState => _connectionState;
  Map<String, DateTime> get deviceLastSeen => Map.unmodifiable(_deviceLastSeen);
  Map<String, Map<String, dynamic>> get deviceData =>
      Map.unmodifiable(_deviceData);

  MQTTService(this.clientId) {
    client = MqttServerClient(AppConstants.mqttBroker, clientId);
    client.port = AppConstants.mqttPort;
    client.logging(on: false); // Disable verbose logging
    client.onDisconnected = _onDisconnected;
    client.onConnected = _onConnected;
    client.onSubscribed = _onSubscribed;
    client.keepAlivePeriod = 30;
    client.autoReconnect = true;
  }

  Future<void> connect() async {
    try {
      _setConnectionState(MQTTConnectionState.connecting);
      await client.connect();
    } catch (e) {
      _setConnectionState(MQTTConnectionState.error);
      _scheduleReconnect();
    }
  }

  void _onConnected() {
    _setConnectionState(MQTTConnectionState.connected);
    _cancelReconnectTimer();

    // Subscribe to device topics
    client.subscribe(AppConstants.deviceStatusTopic, MqttQos.atLeastOnce);
    client.subscribe(AppConstants.deviceAnnounceTopic, MqttQos.atLeastOnce);

    // Listen for messages
    client.updates!.listen((List<MqttReceivedMessage<MqttMessage>> c) {
      final message = c[0].payload as MqttPublishMessage;
      final payload =
          MqttPublishPayload.bytesToStringAsString(message.payload.message);
      _handleMessage(c[0].topic, payload);
    });

    // Start heartbeat timer
    _startHeartbeat();
  }

  void _onDisconnected() {
    _setConnectionState(MQTTConnectionState.disconnected);
    _cancelHeartbeatTimer();
    _scheduleReconnect();
  }

  void _onSubscribed(String topic) {
    // Topic subscribed successfully
  }

  void _handleMessage(String topic, String payload) {
    try {
      // Extract device ID from topic
      final topicParts = topic.split('/');
      if (topicParts.length >= 3) {
        final deviceId = topicParts[1];
        final messageType = topicParts[2];

        // Update device last seen
        _deviceLastSeen[deviceId] = DateTime.now();

        if (messageType == 'status') {
          // Parse JSON sensor data
          try {
            final data = json.decode(payload) as Map<String, dynamic>;
            _deviceData[deviceId] = data;
            onDeviceData?.call(deviceId, data);
          } catch (e) {
            // Handle non-JSON status messages
            _deviceData[deviceId] = {'status': payload};
          }
        }

        // Call the general message callback
        onMessage?.call(topic, payload);
      }
    } catch (e) {
      // Handle message parsing errors
    }
  }

  void _setConnectionState(MQTTConnectionState state) {
    if (_connectionState != state) {
      _connectionState = state;
      onConnectionStateChanged?.call(state);
    }
  }

  void _scheduleReconnect() {
    _cancelReconnectTimer();
    _reconnectTimer = Timer(
      Duration(seconds: AppConstants.mqttReconnectInterval),
      () => connect(),
    );
  }

  void _cancelReconnectTimer() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }

  void _startHeartbeat() {
    _cancelHeartbeatTimer();
    _heartbeatTimer = Timer.periodic(
      const Duration(seconds: 30),
      (timer) {
        if (client.connectionStatus?.state == MqttConnectionState.connected) {
          // Send heartbeat
          publish('system/heartbeat', DateTime.now().toIso8601String());
        }
      },
    );
  }

  void _cancelHeartbeatTimer() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  void publish(String topic, String message) {
    if (client.connectionStatus?.state == MqttConnectionState.connected) {
      final builder = MqttClientPayloadBuilder();
      builder.addString(message);
      client.publishMessage(topic, MqttQos.atLeastOnce, builder.payload!);
    }
  }

  void publishDeviceControl(String deviceId, String command) {
    final topic =
        AppConstants.deviceControlTopic.replaceAll('{deviceId}', deviceId);
    publish(topic, command);
  }

  void turnDeviceOn(String deviceId) {
    publishDeviceControl(deviceId, AppConstants.relayOnCommand);
  }

  void turnDeviceOff(String deviceId) {
    publishDeviceControl(deviceId, AppConstants.relayOffCommand);
  }

  void resetDevice(String deviceId) {
    publishDeviceControl(deviceId, AppConstants.resetCommand);
  }

  bool isDeviceOnline(String deviceId) {
    final lastSeen = _deviceLastSeen[deviceId];
    if (lastSeen == null) return false;

    final timeDiff = DateTime.now().difference(lastSeen);
    return timeDiff.inMinutes < 5; // Consider offline if no data for 5 minutes
  }

  Map<String, dynamic>? getDeviceData(String deviceId) {
    return _deviceData[deviceId];
  }

  void disconnect() {
    _cancelReconnectTimer();
    _cancelHeartbeatTimer();
    client.disconnect();
    _setConnectionState(MQTTConnectionState.disconnected);
  }
}
