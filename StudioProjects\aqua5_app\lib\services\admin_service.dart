import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../models/device_model.dart';
import '../models/analytics_model.dart';
import '../mqtt_service.dart';
import 'firestore_service.dart';

class AdminService extends ChangeNotifier {
  static final AdminService _instance = AdminService._internal();
  factory AdminService() => _instance;
  AdminService._internal();

  final FirestoreService _firestoreService = FirestoreService();
  MQTTService? _mqttService;

  // Data
  List<UserModel> _users = [];
  List<DeviceModel> _devices = [];
  List<AnalyticsModel> _analytics = [];
  List<SystemLogModel> _systemLogs = [];
  
  // Loading states
  bool _isLoadingUsers = false;
  bool _isLoadingDevices = false;
  bool _isLoadingAnalytics = false;
  bool _isLoadingLogs = false;

  // Error states
  String? _usersError;
  String? _devicesError;
  String? _analyticsError;
  String? _logsError;

  // Getters
  List<UserModel> get users => List.unmodifiable(_users);
  List<DeviceModel> get devices => List.unmodifiable(_devices);
  List<AnalyticsModel> get analytics => List.unmodifiable(_analytics);
  List<SystemLogModel> get systemLogs => List.unmodifiable(_systemLogs);

  bool get isLoadingUsers => _isLoadingUsers;
  bool get isLoadingDevices => _isLoadingDevices;
  bool get isLoadingAnalytics => _isLoadingAnalytics;
  bool get isLoadingLogs => _isLoadingLogs;

  String? get usersError => _usersError;
  String? get devicesError => _devicesError;
  String? get analyticsError => _analyticsError;
  String? get logsError => _logsError;

  // Statistics
  int get totalUsers => _users.length;
  int get activeUsers => _users.where((u) => u.isActive).length;
  int get totalDevices => _devices.length;
  int get onlineDevices => _devices.where((d) => d.isOnline).length;
  double get totalRevenue => _analytics.isNotEmpty 
      ? _analytics.map((a) => a.revenue).reduce((a, b) => a + b) 
      : 0.0;

  void setMqttService(MQTTService mqttService) {
    _mqttService = mqttService;
    _mqttService?.onDeviceData = _handleDeviceData;
  }

  // Load all data
  Future<void> loadAllData() async {
    await Future.wait([
      loadUsers(),
      loadDevices(),
      loadAnalytics(),
      loadSystemLogs(),
    ]);
  }

  // User Management
  Future<void> loadUsers() async {
    _isLoadingUsers = true;
    _usersError = null;
    notifyListeners();

    try {
      _users = await _firestoreService.getAllUsers();
      _usersError = null;
    } catch (e) {
      _usersError = e.toString();
    } finally {
      _isLoadingUsers = false;
      notifyListeners();
    }
  }

  Future<void> updateUser(String userId, UserModel user) async {
    try {
      await _firestoreService.updateUser(userId, user);
      await loadUsers(); // Refresh the list
      
      // Log the action
      await _addSystemLog(
        level: 'INFO',
        category: 'USER',
        message: 'User ${user.email} updated by admin',
        userId: userId,
      );
    } catch (e) {
      throw Exception('Failed to update user: $e');
    }
  }

  Future<List<UserModel>> searchUsers(String query) async {
    try {
      return await _firestoreService.searchUsers(query);
    } catch (e) {
      throw Exception('Failed to search users: $e');
    }
  }

  // Device Management
  Future<void> loadDevices() async {
    _isLoadingDevices = true;
    _devicesError = null;
    notifyListeners();

    try {
      _devices = await _firestoreService.getAllDevices();
      _devicesError = null;
    } catch (e) {
      _devicesError = e.toString();
    } finally {
      _isLoadingDevices = false;
      notifyListeners();
    }
  }

  Future<void> controlDevice(String deviceId, String command) async {
    try {
      if (_mqttService == null) {
        throw Exception('MQTT service not available');
      }

      switch (command.toUpperCase()) {
        case 'ON':
          _mqttService!.turnDeviceOn(deviceId);
          break;
        case 'OFF':
          _mqttService!.turnDeviceOff(deviceId);
          break;
        case 'RESET':
          _mqttService!.resetDevice(deviceId);
          break;
        default:
          throw Exception('Unknown command: $command');
      }

      // Log the action
      await _addSystemLog(
        level: 'INFO',
        category: 'DEVICE',
        message: 'Device $deviceId command: $command',
        deviceId: deviceId,
      );
    } catch (e) {
      throw Exception('Failed to control device: $e');
    }
  }

  void _handleDeviceData(String deviceId, Map<String, dynamic> data) {
    // Update device status in Firestore
    _firestoreService.updateDeviceStatus(deviceId, true, data);
    
    // Update local device data
    final deviceIndex = _devices.indexWhere((d) => d.id == deviceId);
    if (deviceIndex != -1) {
      final device = _devices[deviceIndex];
      _devices[deviceIndex] = device.copyWith(
        isOnline: true,
        lastSeen: DateTime.now(),
        tdsLevel: data['tds']?.toDouble() ?? device.tdsLevel,
        flowRate: data['flow']?.toDouble() ?? device.flowRate,
        relayState: data['relay'] == 'ON',
        sensorData: data,
      );
      notifyListeners();
    }
  }

  // Analytics
  Future<void> loadAnalytics({int days = 7}) async {
    _isLoadingAnalytics = true;
    _analyticsError = null;
    notifyListeners();

    try {
      _analytics = await _firestoreService.getAnalytics(days: days);
      _analyticsError = null;
    } catch (e) {
      _analyticsError = e.toString();
    } finally {
      _isLoadingAnalytics = false;
      notifyListeners();
    }
  }

  Future<void> generateDailyAnalytics() async {
    try {
      final today = DateTime.now();
      final analytics = AnalyticsModel(
        id: '',
        date: today,
        totalWaterUsage: _calculateTotalWaterUsage(),
        activeDevices: onlineDevices,
        totalUsers: totalUsers,
        activeUsers: activeUsers,
        revenue: _calculateDailyRevenue(),
        planSubscriptions: _calculatePlanSubscriptions(),
        deviceUsage: _calculateDeviceUsage(),
        hourlyUsage: _calculateHourlyUsage(),
        topUsers: _getTopUsers(),
        averageTDS: _calculateAverageTDS(),
        systemAlerts: _systemLogs.where((log) => log.isError).length,
        createdAt: today,
      );

      await _firestoreService.saveAnalytics(analytics);
      await loadAnalytics(); // Refresh analytics
    } catch (e) {
      throw Exception('Failed to generate analytics: $e');
    }
  }

  // System Logs
  Future<void> loadSystemLogs({int limit = 100}) async {
    _isLoadingLogs = true;
    _logsError = null;
    notifyListeners();

    try {
      _systemLogs = await _firestoreService.getSystemLogs(limit: limit);
      _logsError = null;
    } catch (e) {
      _logsError = e.toString();
    } finally {
      _isLoadingLogs = false;
      notifyListeners();
    }
  }

  Future<void> _addSystemLog({
    required String level,
    required String category,
    required String message,
    String? deviceId,
    String? userId,
    Map<String, dynamic>? metadata,
  }) async {
    final log = SystemLogModel(
      id: '',
      level: level,
      category: category,
      message: message,
      deviceId: deviceId,
      userId: userId,
      metadata: metadata ?? {},
      timestamp: DateTime.now(),
    );

    await _firestoreService.addSystemLog(log);
    _systemLogs.insert(0, log); // Add to beginning of list
    notifyListeners();
  }

  // Helper methods for analytics calculations
  double _calculateTotalWaterUsage() {
    return _devices.fold(0.0, (sum, device) => sum + device.flowRate);
  }

  double _calculateDailyRevenue() {
    // This would typically come from payment records
    return _users.length * 10.0; // Mock calculation
  }

  Map<String, int> _calculatePlanSubscriptions() {
    final planCounts = <String, int>{};
    for (final user in _users) {
      planCounts[user.subscriptionPlanName] = 
          (planCounts[user.subscriptionPlanName] ?? 0) + 1;
    }
    return planCounts;
  }

  Map<String, double> _calculateDeviceUsage() {
    final deviceUsage = <String, double>{};
    for (final device in _devices) {
      deviceUsage[device.id] = device.flowRate;
    }
    return deviceUsage;
  }

  Map<String, int> _calculateHourlyUsage() {
    // Mock hourly usage data
    final hourlyUsage = <String, int>{};
    for (int i = 0; i < 24; i++) {
      hourlyUsage[i.toString()] = (i * 2) + 10; // Mock data
    }
    return hourlyUsage;
  }

  List<Map<String, dynamic>> _getTopUsers() {
    final sortedUsers = List<UserModel>.from(_users)
      ..sort((a, b) => b.totalUsage.compareTo(a.totalUsage));
    
    return sortedUsers.take(5).map((user) => {
      'email': user.email,
      'usage': user.totalUsage,
      'plan': user.subscriptionPlanName,
    }).toList();
  }

  double _calculateAverageTDS() {
    if (_devices.isEmpty) return 0.0;
    final totalTDS = _devices.fold(0.0, (sum, device) => sum + device.tdsLevel);
    return totalTDS / _devices.length;
  }

  // Refresh all data
  Future<void> refreshAllData() async {
    await loadAllData();
  }

  @override
  void dispose() {
    super.dispose();
  }
}
