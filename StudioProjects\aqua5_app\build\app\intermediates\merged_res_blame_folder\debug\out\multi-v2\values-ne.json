{"logs": [{"outputFile": "com.example.aqua5_app-mergeDebugResources-49:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68e142ef8359c595e6a0cf974c40276a\\transformed\\preference-1.2.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,188,272,353,500,669,767", "endColumns": "82,83,80,146,168,97,79", "endOffsets": "183,267,348,495,664,762,842"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6158,6348,6766,6847,7174,7343,7441", "endColumns": "82,83,80,146,168,97,79", "endOffsets": "6236,6427,6842,6989,7338,7436,7516"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ab921d2504dc4ffdb3a2cd4464883cfe\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ne\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4873", "endColumns": "163", "endOffsets": "5032"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ff2b779d4ecf08070f3e2c7f20fd7758\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,111", "endOffsets": "161,273"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2888,2999", "endColumns": "110,111", "endOffsets": "2994,3106"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bb5a5125e57e439427cd66fb993867a0\\transformed\\browser-1.4.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,274,388", "endColumns": "106,111,113,107", "endOffsets": "157,269,383,491"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6241,6432,6544,6658", "endColumns": "106,111,113,107", "endOffsets": "6343,6539,6653,6761"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b2884dbf4ee3398b2907745768baeed2\\transformed\\core-1.13.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3111,3214,3317,3419,3525,3623,3723,7073", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "3209,3312,3414,3520,3618,3718,3826,7169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c84e8a146159499b9c85f9e0df490157\\transformed\\jetified-play-services-base-18.1.0\\res\\values-ne\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,454,584,697,864,996,1102,1203,1379,1489,1649,1778,1922,2070,2132,2200", "endColumns": "106,153,129,112,166,131,105,100,175,109,159,128,143,147,61,67,87", "endOffsets": "299,453,583,696,863,995,1101,1202,1378,1488,1648,1777,1921,2069,2131,2199,2287"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3831,3942,4100,4234,4351,4522,4658,4768,5037,5217,5331,5495,5628,5776,5928,5994,6066", "endColumns": "110,157,133,116,170,135,109,104,179,113,163,132,147,151,65,71,91", "endOffsets": "3937,4095,4229,4346,4517,4653,4763,4868,5212,5326,5490,5623,5771,5923,5989,6061,6153"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d4c03f8eccf892b96fd6fc8b065707c2\\transformed\\appcompat-1.1.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,327,435,526,633,760,844,924,1015,1107,1202,1296,1397,1490,1585,1679,1770,1861,1946,2059,2160,2256,2369,2479,2603,2777,2888", "endColumns": "110,110,107,90,106,126,83,79,90,91,94,93,100,92,94,93,90,90,84,112,100,95,112,109,123,173,110,78", "endOffsets": "211,322,430,521,628,755,839,919,1010,1102,1197,1291,1392,1485,1580,1674,1765,1856,1941,2054,2155,2251,2364,2474,2598,2772,2883,2962"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,327,435,526,633,760,844,924,1015,1107,1202,1296,1397,1490,1585,1679,1770,1861,1946,2059,2160,2256,2369,2479,2603,2777,6994", "endColumns": "110,110,107,90,106,126,83,79,90,91,94,93,100,92,94,93,90,90,84,112,100,95,112,109,123,173,110,78", "endOffsets": "211,322,430,521,628,755,839,919,1010,1102,1197,1291,1392,1485,1580,1674,1765,1856,1941,2054,2155,2251,2364,2474,2598,2772,2883,7068"}}]}]}