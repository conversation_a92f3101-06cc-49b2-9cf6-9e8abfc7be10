import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wifi_iot/wifi_iot.dart';

enum WiFiConfigStatus {
  idle,
  scanning,
  connecting,
  configuring,
  completed,
  error
}

class WiFiNetwork {
  final String ssid;
  final String bssid;
  final int level;
  final int frequency;
  final String capabilities;
  final bool isSecure;

  WiFiNetwork({
    required this.ssid,
    required this.bssid,
    required this.level,
    required this.frequency,
    required this.capabilities,
    required this.isSecure,
  });

  int get signalStrength {
    // Convert dBm to percentage (rough approximation)
    if (level >= -50) return 100;
    if (level >= -60) return 75;
    if (level >= -70) return 50;
    if (level >= -80) return 25;
    return 10;
  }

  String get signalStrengthText {
    if (signalStrength >= 75) return 'Excellent';
    if (signalStrength >= 50) return 'Good';
    if (signalStrength >= 25) return 'Fair';
    return 'Poor';
  }

  factory WiFiNetwork.fromMap(Map<String, dynamic> map) {
    return WiFiNetwork(
      ssid: map['ssid'] ?? '',
      bssid: map['bssid'] ?? '',
      level: map['level'] ?? 0,
      frequency: map['frequency'] ?? 0,
      capabilities: map['capabilities'] ?? '',
      isSecure: (map['capabilities'] ?? '').contains('WPA') || 
                (map['capabilities'] ?? '').contains('WEP'),
    );
  }

  @override
  String toString() {
    return 'WiFiNetwork(ssid: $ssid, level: $level, secure: $isSecure)';
  }
}

class WiFiConfigResult {
  final bool success;
  final String? errorMessage;
  final Map<String, dynamic>? data;

  WiFiConfigResult({
    required this.success,
    this.errorMessage,
    this.data,
  });

  factory WiFiConfigResult.success({Map<String, dynamic>? data}) {
    return WiFiConfigResult(success: true, data: data);
  }

  factory WiFiConfigResult.error(String message) {
    return WiFiConfigResult(success: false, errorMessage: message);
  }
}

class WiFiConfigService extends ChangeNotifier {
  static final WiFiConfigService _instance = WiFiConfigService._internal();
  factory WiFiConfigService() => _instance;
  WiFiConfigService._internal();

  final NetworkInfo _networkInfo = NetworkInfo();
  
  WiFiConfigStatus _status = WiFiConfigStatus.idle;
  List<WiFiNetwork> _availableNetworks = [];
  String? _currentSSID;
  String? _errorMessage;
  double _progress = 0.0;

  WiFiConfigStatus get status => _status;
  List<WiFiNetwork> get availableNetworks => List.unmodifiable(_availableNetworks);
  String? get currentSSID => _currentSSID;
  String? get errorMessage => _errorMessage;
  double get progress => _progress;

  /// Initialize WiFi service and request permissions
  Future<bool> initialize() async {
    try {
      // Request location permission (required for WiFi scanning on Android)
      final locationStatus = await Permission.location.request();
      if (locationStatus != PermissionStatus.granted) {
        _errorMessage = 'Location permission is required for WiFi scanning';
        return false;
      }

      // Request nearby WiFi devices permission (Android 13+)
      if (Platform.isAndroid) {
        await Permission.nearbyWifiDevices.request();
      }

      // Get current WiFi info
      await _getCurrentWiFiInfo();
      
      return true;
    } catch (e) {
      _errorMessage = 'Failed to initialize WiFi service: $e';
      debugPrint(_errorMessage);
      return false;
    }
  }

  /// Get current WiFi information
  Future<void> _getCurrentWiFiInfo() async {
    try {
      _currentSSID = await _networkInfo.getWifiName();
      // Remove quotes if present
      if (_currentSSID != null && _currentSSID!.startsWith('"') && _currentSSID!.endsWith('"')) {
        _currentSSID = _currentSSID!.substring(1, _currentSSID!.length - 1);
      }
      notifyListeners();
    } catch (e) {
      debugPrint('Failed to get current WiFi info: $e');
    }
  }

  /// Scan for available WiFi networks
  Future<WiFiConfigResult> scanNetworks() async {
    try {
      _setStatus(WiFiConfigStatus.scanning);
      _setProgress(0.1);

      if (!Platform.isAndroid && !Platform.isIOS) {
        return WiFiConfigResult.error('WiFi scanning not supported on this platform');
      }

      _setProgress(0.3);

      // Check if WiFi is enabled
      final isEnabled = await WiFiForIoTPlugin.isEnabled();
      if (!isEnabled) {
        return WiFiConfigResult.error('WiFi is not enabled. Please enable WiFi and try again.');
      }

      _setProgress(0.5);

      // Scan for networks
      final networks = await WiFiForIoTPlugin.loadWifiList();
      
      _setProgress(0.8);

      // Convert to our WiFiNetwork objects and filter out empty SSIDs
      _availableNetworks = networks
          .where((network) => network.ssid?.isNotEmpty == true)
          .map((network) => WiFiNetwork(
                ssid: network.ssid!,
                bssid: network.bssid ?? '',
                level: network.level ?? 0,
                frequency: network.frequency ?? 0,
                capabilities: network.capabilities ?? '',
                isSecure: (network.capabilities ?? '').contains('WPA') || 
                         (network.capabilities ?? '').contains('WEP'),
              ))
          .toList();

      // Sort by signal strength
      _availableNetworks.sort((a, b) => b.level.compareTo(a.level));

      _setProgress(1.0);
      _setStatus(WiFiConfigStatus.idle);

      return WiFiConfigResult.success(data: {
        'networkCount': _availableNetworks.length,
      });

    } catch (e) {
      _setStatus(WiFiConfigStatus.error);
      _errorMessage = 'Failed to scan networks: $e';
      return WiFiConfigResult.error(_errorMessage!);
    }
  }

  /// Connect to WiFi network
  Future<WiFiConfigResult> connectToNetwork({
    required String ssid,
    required String password,
    bool isSecure = true,
  }) async {
    try {
      _setStatus(WiFiConfigStatus.connecting);
      _setProgress(0.1);

      // Validate inputs
      if (ssid.trim().isEmpty) {
        return WiFiConfigResult.error('SSID cannot be empty');
      }

      if (isSecure && password.trim().isEmpty) {
        return WiFiConfigResult.error('Password is required for secure networks');
      }

      _setProgress(0.3);

      // Connect to network
      bool connected;
      if (isSecure) {
        connected = await WiFiForIoTPlugin.connect(
          ssid,
          password: password,
          security: NetworkSecurity.WPA,
          joinOnce: false,
        );
      } else {
        connected = await WiFiForIoTPlugin.connect(
          ssid,
          security: NetworkSecurity.NONE,
          joinOnce: false,
        );
      }

      _setProgress(0.7);

      if (!connected) {
        return WiFiConfigResult.error('Failed to connect to network. Please check your credentials.');
      }

      // Wait a moment for connection to stabilize
      await Future.delayed(const Duration(seconds: 2));

      _setProgress(0.9);

      // Verify connection
      final currentNetwork = await _networkInfo.getWifiName();
      final isConnected = currentNetwork != null && 
                         currentNetwork.replaceAll('"', '') == ssid;

      if (!isConnected) {
        return WiFiConfigResult.error('Connection verification failed');
      }

      _setProgress(1.0);
      _setStatus(WiFiConfigStatus.completed);
      _currentSSID = ssid;

      return WiFiConfigResult.success(data: {
        'ssid': ssid,
        'connected': true,
      });

    } catch (e) {
      _setStatus(WiFiConfigStatus.error);
      _errorMessage = 'Failed to connect to network: $e';
      return WiFiConfigResult.error(_errorMessage!);
    }
  }

  /// Send WiFi configuration to ESP32 device
  Future<WiFiConfigResult> configureESP32Device({
    required String deviceId,
    required String ssid,
    required String password,
    required String mqttBroker,
    required int mqttPort,
  }) async {
    try {
      _setStatus(WiFiConfigStatus.configuring);
      _setProgress(0.1);

      // Create configuration payload
      final config = {
        'deviceId': deviceId,
        'wifi': {
          'ssid': ssid,
          'password': password,
        },
        'mqtt': {
          'broker': mqttBroker,
          'port': mqttPort,
          'topics': {
            'status': 'device/$deviceId/status',
            'control': 'device/$deviceId/control',
            'announce': 'device/$deviceId/announce',
          }
        },
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      _setProgress(0.5);

      // In a real implementation, you would send this configuration to the ESP32
      // This could be done via:
      // 1. Bluetooth Low Energy (BLE)
      // 2. Temporary WiFi hotspot created by ESP32
      // 3. USB connection
      // 4. QR code that ESP32 scans

      // For now, we'll simulate the configuration process
      await Future.delayed(const Duration(seconds: 3));

      _setProgress(1.0);
      _setStatus(WiFiConfigStatus.completed);

      return WiFiConfigResult.success(data: {
        'deviceId': deviceId,
        'configured': true,
        'config': config,
      });

    } catch (e) {
      _setStatus(WiFiConfigStatus.error);
      _errorMessage = 'Failed to configure ESP32 device: $e';
      return WiFiConfigResult.error(_errorMessage!);
    }
  }

  /// Get WiFi configuration as JSON string for ESP32
  String getConfigurationJSON({
    required String deviceId,
    required String ssid,
    required String password,
    required String mqttBroker,
    required int mqttPort,
  }) {
    final config = {
      'deviceId': deviceId,
      'wifi': {
        'ssid': ssid,
        'password': password,
      },
      'mqtt': {
        'broker': mqttBroker,
        'port': mqttPort,
        'clientId': 'aqua5_$deviceId',
        'topics': {
          'status': 'device/$deviceId/status',
          'control': 'device/$deviceId/control',
          'announce': 'device/$deviceId/announce',
        }
      },
      'device': {
        'name': 'Aqua5 Water Monitor',
        'version': '1.0.0',
        'type': 'water_monitor',
      },
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    return jsonEncode(config);
  }

  /// Reset service state
  void resetState() {
    _status = WiFiConfigStatus.idle;
    _errorMessage = null;
    _progress = 0.0;
    _availableNetworks.clear();
    notifyListeners();
  }

  void _setStatus(WiFiConfigStatus status) {
    _status = status;
    notifyListeners();
  }

  void _setProgress(double progress) {
    _progress = progress;
    notifyListeners();
  }
}
