import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/user_model.dart';
import 'firestore_service.dart';
import 'fcm_service.dart';

/// Authentication result class to handle both success and error states
class AuthResult {
  final bool success;
  final String? errorMessage;
  final User? user;

  AuthResult({
    required this.success,
    this.errorMessage,
    this.user,
  });

  factory AuthResult.success(User user) {
    return AuthResult(
      success: true,
      user: user,
    );
  }

  factory AuthResult.error(String message) {
    return AuthResult(
      success: false,
      errorMessage: message,
    );
  }
}

/// Service class to handle all Firebase authentication operations
class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirestoreService _firestoreService = FirestoreService();
  final FCMService _fcmService = FCMService();

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Stream of auth state changes
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Sign up with email and password
  Future<AuthResult> signUpWithEmail(String email, String password,
      {String? displayName}) async {
    try {
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      final user = userCredential.user!;

      // Update display name if provided
      if (displayName != null && displayName.isNotEmpty) {
        await user.updateDisplayName(displayName);
      }

      // Create user document in Firestore
      await _createUserDocument(user, displayName: displayName);

      return AuthResult.success(user);
    } on FirebaseAuthException catch (e) {
      return AuthResult.error(_getFirebaseAuthErrorMessage(e));
    } catch (e) {
      return AuthResult.error(
          'An unexpected error occurred. Please try again.');
    }
  }

  // Sign in with email and password
  Future<AuthResult> signInWithEmail(String email, String password) async {
    try {
      final userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      final user = userCredential.user!;

      // Update user document on login
      await _updateUserOnLogin(user);

      return AuthResult.success(user);
    } on FirebaseAuthException catch (e) {
      return AuthResult.error(_getFirebaseAuthErrorMessage(e));
    } catch (e) {
      return AuthResult.error(
          'An unexpected error occurred. Please try again.');
    }
  }

  // Sign in with phone number (send OTP) with reCAPTCHA verification
  Future<AuthResult> sendOTP(String phoneNumber,
      {BuildContext? context}) async {
    try {
      final completer = Completer<AuthResult>();

      // For web, we need to use reCAPTCHA
      // For mobile, we can use the invisible reCAPTCHA or SafetyNet
      // This implementation works for both platforms

      // We'll use a different approach for mobile and web
      // For mobile, we'll use the default Firebase implementation
      // For web, we'll need to add a reCAPTCHA widget to the UI

      await _auth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          // Auto-verification completed (Android only)
          try {
            final userCredential = await _auth.signInWithCredential(credential);
            completer.complete(AuthResult.success(userCredential.user!));
          } catch (e) {
            completer.complete(
                AuthResult.error('Verification failed. Please try again.'));
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          completer.complete(AuthResult.error(_getFirebaseAuthErrorMessage(e)));
        },
        codeSent: (String verificationId, int? resendToken) {
          // Return the verification ID to be used with verifyOTP
          completer.complete(AuthResult(
            success: true,
            user: null,
            errorMessage:
                verificationId, // Using errorMessage to pass verificationId
          ));
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          // Auto-retrieval timeout
          if (!completer.isCompleted) {
            completer.complete(AuthResult.error(
                'Verification code request timed out. Please try again.'));
          }
        },
        timeout: const Duration(seconds: 60),
      );

      return await completer.future;
    } catch (e) {
      return AuthResult.error(
          'Failed to send verification code. Please try again: $e');
    }
  }

  // Send OTP without reCAPTCHA (for resending)
  Future<AuthResult> resendOTP(String phoneNumber) async {
    try {
      final completer = Completer<AuthResult>();

      await _auth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          // Auto-verification completed (Android only)
          try {
            final userCredential = await _auth.signInWithCredential(credential);
            completer.complete(AuthResult.success(userCredential.user!));
          } catch (e) {
            completer.complete(
                AuthResult.error('Verification failed. Please try again.'));
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          completer.complete(AuthResult.error(_getFirebaseAuthErrorMessage(e)));
        },
        codeSent: (String verificationId, int? resendToken) {
          // Return the verification ID to be used with verifyOTP
          completer.complete(AuthResult(
            success: true,
            user: null,
            errorMessage:
                verificationId, // Using errorMessage to pass verificationId
          ));
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          // Auto-retrieval timeout
          if (!completer.isCompleted) {
            completer.complete(AuthResult.error(
                'Verification code request timed out. Please try again.'));
          }
        },
        timeout: const Duration(seconds: 60),
      );

      return await completer.future;
    } catch (e) {
      return AuthResult.error(
          'Failed to resend verification code. Please try again.');
    }
  }

  // Verify OTP
  Future<AuthResult> verifyOTP(String verificationId, String otp) async {
    try {
      final credential = PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: otp,
      );

      final userCredential = await _auth.signInWithCredential(credential);
      return AuthResult.success(userCredential.user!);
    } on FirebaseAuthException catch (e) {
      return AuthResult.error(_getFirebaseAuthErrorMessage(e));
    } catch (e) {
      return AuthResult.error('Failed to verify code. Please try again.');
    }
  }

  // Reset password
  Future<AuthResult> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      return AuthResult(
        success: true,
        errorMessage: 'Password reset email sent to $email',
      );
    } on FirebaseAuthException catch (e) {
      return AuthResult.error(_getFirebaseAuthErrorMessage(e));
    } catch (e) {
      return AuthResult.error(
          'Failed to send password reset email. Please try again.');
    }
  }

  // Sign out
  Future<void> signOut() async {
    await _auth.signOut();
  }

  // Update user profile
  Future<AuthResult> updateProfile(
      {String? displayName, String? photoURL}) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return AuthResult.error('No user is currently signed in.');
      }

      await user.updateDisplayName(displayName);
      await user.updatePhotoURL(photoURL);

      return AuthResult.success(user);
    } catch (e) {
      return AuthResult.error('Failed to update profile. Please try again.');
    }
  }

  // Create user document in Firestore
  Future<void> _createUserDocument(User user, {String? displayName}) async {
    try {
      // Get FCM token
      final fcmToken = _fcmService.currentToken ?? '';

      // Get default subscription plan
      final defaultPlan = await _firestoreService.getDefaultSubscriptionPlan();

      final userModel = UserModel(
        id: user.uid,
        email: user.email ?? '',
        displayName: displayName ?? user.displayName ?? '',
        phoneNumber: user.phoneNumber ?? '',
        isActive: true,
        isAdmin: user.email == '<EMAIL>', // Set admin based on email
        subscriptionPlanId: defaultPlan?.id ?? '',
        subscriptionPlanName: defaultPlan?.planName ?? 'Basic',
        dailyQuota: defaultPlan?.dailyUsage ?? 10.0,
        currentDailyUsage: 0.0,
        subscriptionStartDate: DateTime.now(),
        subscriptionEndDate: DateTime.now()
            .add(Duration(days: (defaultPlan?.planValidityMonths ?? 1) * 30)),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        deviceIds: [],
        preferences: {},
        totalUsage: 0.0,
        loginCount: 1,
        fcmToken: fcmToken,
        maxDevices: _getMaxDevicesForPlan(defaultPlan?.planName ?? 'Basic'),
        deviceSetupHistory: {},
      );

      await _firestoreService.createUser(user.uid, userModel);

      // Subscribe to user notifications
      if (fcmToken.isNotEmpty) {
        await _fcmService.subscribeToUserNotifications(user.uid);
      }
    } catch (e) {
      debugPrint('Failed to create user document: $e');
      // Don't throw error to avoid breaking authentication flow
    }
  }

  // Update user document on login
  Future<void> _updateUserOnLogin(User user) async {
    try {
      // Get or create user document
      final existingUser = await _firestoreService.getUserById(user.uid);

      if (existingUser == null) {
        // Create new user document if it doesn't exist
        await _createUserDocument(user);
      } else {
        // Update existing user document
        final fcmToken = _fcmService.currentToken ?? '';

        final updatedUser = existingUser.copyWith(
          lastLoginAt: DateTime.now(),
          loginCount: existingUser.loginCount + 1,
          fcmToken: fcmToken.isNotEmpty ? fcmToken : existingUser.fcmToken,
          updatedAt: DateTime.now(),
        );

        await _firestoreService.updateUser(user.uid, updatedUser);

        // Update FCM token if changed
        if (fcmToken.isNotEmpty && fcmToken != existingUser.fcmToken) {
          await _fcmService.updateUserToken(fcmToken, userId: user.uid);
        }
      }
    } catch (e) {
      debugPrint('Failed to update user on login: $e');
      // Don't throw error to avoid breaking authentication flow
    }
  }

  // Get max devices based on subscription plan
  int _getMaxDevicesForPlan(String planName) {
    switch (planName.toLowerCase()) {
      case 'basic':
        return 1;
      case 'premium':
        return 3;
      case 'enterprise':
        return 10;
      default:
        return 1;
    }
  }

  // Get current user model from Firestore
  Future<UserModel?> getCurrentUserModel() async {
    final user = currentUser;
    if (user == null) return null;

    try {
      return await _firestoreService.getUserById(user.uid);
    } catch (e) {
      debugPrint('Failed to get current user model: $e');
      return null;
    }
  }

  // Helper method to get user-friendly error messages
  String _getFirebaseAuthErrorMessage(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'No user found with this email.';
      case 'wrong-password':
        return 'Incorrect password.';
      case 'email-already-in-use':
        return 'This email is already registered.';
      case 'invalid-email':
        return 'Please enter a valid email address.';
      case 'weak-password':
        return 'Password is too weak. Please use a stronger password.';
      case 'operation-not-allowed':
        return 'This operation is not allowed.';
      case 'invalid-verification-code':
        return 'The verification code is invalid. Please try again.';
      case 'invalid-verification-id':
        return 'The verification ID is invalid. Please request a new code.';
      case 'too-many-requests':
        return 'Too many requests. Please try again later.';
      case 'network-request-failed':
        return 'Network error. Please check your connection and try again.';
      default:
        return e.message ?? 'An error occurred. Please try again.';
    }
  }
}
