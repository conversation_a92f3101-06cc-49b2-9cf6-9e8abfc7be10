import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../models/device_model.dart';
import '../models/user_model.dart';
import 'firestore_service.dart';
import 'auth_service.dart';
import 'fcm_service.dart';

enum DeviceRegistrationStatus {
  idle,
  scanning,
  connecting,
  configuring,
  registering,
  completed,
  error
}

class DeviceRegistrationResult {
  final bool success;
  final String? deviceId;
  final String? errorMessage;
  final DeviceModel? device;

  DeviceRegistrationResult({
    required this.success,
    this.deviceId,
    this.errorMessage,
    this.device,
  });

  factory DeviceRegistrationResult.success(String deviceId, DeviceModel device) {
    return DeviceRegistrationResult(
      success: true,
      deviceId: deviceId,
      device: device,
    );
  }

  factory DeviceRegistrationResult.error(String message) {
    return DeviceRegistrationResult(
      success: false,
      errorMessage: message,
    );
  }
}

class DeviceRegistrationService extends ChangeNotifier {
  static final DeviceRegistrationService _instance = DeviceRegistrationService._internal();
  factory DeviceRegistrationService() => _instance;
  DeviceRegistrationService._internal();

  final FirestoreService _firestoreService = FirestoreService();
  final AuthService _authService = AuthService();
  final FCMService _fcmService = FCMService();
  final Uuid _uuid = const Uuid();

  DeviceRegistrationStatus _status = DeviceRegistrationStatus.idle;
  String? _currentDeviceId;
  String? _errorMessage;
  double _progress = 0.0;

  DeviceRegistrationStatus get status => _status;
  String? get currentDeviceId => _currentDeviceId;
  String? get errorMessage => _errorMessage;
  double get progress => _progress;

  /// Generate a unique device ID
  String generateDeviceId() {
    return _uuid.v4();
  }

  /// Register a new device
  Future<DeviceRegistrationResult> registerDevice({
    required String deviceName,
    required String location,
    String deviceType = 'Water Monitor',
    String? customDeviceId,
  }) async {
    try {
      _setStatus(DeviceRegistrationStatus.registering);
      _setProgress(0.1);

      // Get current user
      final user = _authService.currentUser;
      if (user == null) {
        return DeviceRegistrationResult.error('User not authenticated');
      }

      _setProgress(0.2);

      // Get user model to check device limits
      final userModel = await _authService.getCurrentUserModel();
      if (userModel == null) {
        return DeviceRegistrationResult.error('User profile not found');
      }

      _setProgress(0.3);

      // Check if user can add more devices
      if (!userModel.canAddMoreDevices) {
        return DeviceRegistrationResult.error(
          'Device limit reached. You can add up to ${userModel.maxDevices} devices with your ${userModel.subscriptionPlanName} plan.'
        );
      }

      _setProgress(0.4);

      // Generate device ID
      final deviceId = customDeviceId ?? generateDeviceId();
      _currentDeviceId = deviceId;

      _setProgress(0.5);

      // Check if device ID already exists
      final existingDevice = await _checkDeviceExists(deviceId);
      if (existingDevice) {
        return DeviceRegistrationResult.error('Device ID already exists');
      }

      _setProgress(0.6);

      // Create device model
      final device = DeviceModel(
        id: deviceId,
        name: deviceName,
        userId: user.uid,
        userEmail: user.email ?? '',
        isOnline: false,
        relayState: false,
        tdsLevel: 0.0,
        flowRate: 0.0,
        temperature: 0.0,
        pressure: 0.0,
        lastSeen: DateTime.now(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        sensorData: {},
        location: location,
        firmwareVersion: '',
      );

      _setProgress(0.7);

      // Save device to Firestore
      await _firestoreService.addDevice(device);

      _setProgress(0.8);

      // Update user's device list
      final updatedDeviceIds = List<String>.from(userModel.deviceIds)..add(deviceId);
      final updatedUser = userModel.copyWith(
        deviceIds: updatedDeviceIds,
        updatedAt: DateTime.now(),
        deviceSetupHistory: {
          ...userModel.deviceSetupHistory,
          deviceId: {
            'registeredAt': DateTime.now().toIso8601String(),
            'deviceName': deviceName,
            'location': location,
          }
        },
      );

      await _firestoreService.updateUser(user.uid, updatedUser);

      _setProgress(0.9);

      // Subscribe to device notifications
      await _fcmService.subscribeToDeviceTopic(deviceId);

      _setProgress(1.0);
      _setStatus(DeviceRegistrationStatus.completed);

      return DeviceRegistrationResult.success(deviceId, device);

    } catch (e) {
      _setStatus(DeviceRegistrationStatus.error);
      _errorMessage = e.toString();
      return DeviceRegistrationResult.error('Failed to register device: $e');
    }
  }

  /// Check if device ID already exists
  Future<bool> _checkDeviceExists(String deviceId) async {
    try {
      final device = await _firestoreService.getDeviceById(deviceId);
      return device != null;
    } catch (e) {
      return false;
    }
  }

  /// Remove device from user account
  Future<bool> removeDevice(String deviceId) async {
    try {
      final user = _authService.currentUser;
      if (user == null) return false;

      final userModel = await _authService.getCurrentUserModel();
      if (userModel == null) return false;

      // Remove device from Firestore
      await _firestoreService.deleteDevice(deviceId);

      // Update user's device list
      final updatedDeviceIds = List<String>.from(userModel.deviceIds)
        ..remove(deviceId);
      
      final updatedUser = userModel.copyWith(
        deviceIds: updatedDeviceIds,
        updatedAt: DateTime.now(),
      );

      await _firestoreService.updateUser(user.uid, updatedUser);

      // Unsubscribe from device notifications
      await _fcmService.unsubscribeFromDeviceTopic(deviceId);

      return true;
    } catch (e) {
      debugPrint('Failed to remove device: $e');
      return false;
    }
  }

  /// Get user's devices
  Future<List<DeviceModel>> getUserDevices() async {
    try {
      final user = _authService.currentUser;
      if (user == null) return [];

      return await _firestoreService.getUserDevices(user.uid);
    } catch (e) {
      debugPrint('Failed to get user devices: $e');
      return [];
    }
  }

  /// Transfer device ownership (admin only)
  Future<bool> transferDeviceOwnership(String deviceId, String newUserId) async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) return false;

      final currentUserModel = await _authService.getCurrentUserModel();
      if (currentUserModel == null || !currentUserModel.isAdmin) {
        return false;
      }

      // Get device
      final device = await _firestoreService.getDeviceById(deviceId);
      if (device == null) return false;

      // Get new user
      final newUser = await _firestoreService.getUserById(newUserId);
      if (newUser == null) return false;

      // Check if new user can add more devices
      if (!newUser.canAddMoreDevices) return false;

      // Get old user
      final oldUser = await _firestoreService.getUserById(device.userId);

      // Update device ownership
      final updatedDevice = device.copyWith(
        userId: newUserId,
        userEmail: newUser.email,
        updatedAt: DateTime.now(),
      );

      await _firestoreService.updateDevice(deviceId, updatedDevice);

      // Update old user's device list
      if (oldUser != null) {
        final oldUserDeviceIds = List<String>.from(oldUser.deviceIds)
          ..remove(deviceId);
        
        final updatedOldUser = oldUser.copyWith(
          deviceIds: oldUserDeviceIds,
          updatedAt: DateTime.now(),
        );

        await _firestoreService.updateUser(device.userId, updatedOldUser);
      }

      // Update new user's device list
      final newUserDeviceIds = List<String>.from(newUser.deviceIds)
        ..add(deviceId);
      
      final updatedNewUser = newUser.copyWith(
        deviceIds: newUserDeviceIds,
        updatedAt: DateTime.now(),
      );

      await _firestoreService.updateUser(newUserId, updatedNewUser);

      return true;
    } catch (e) {
      debugPrint('Failed to transfer device ownership: $e');
      return false;
    }
  }

  /// Validate device registration data
  String? validateDeviceData({
    required String deviceName,
    required String location,
  }) {
    if (deviceName.trim().isEmpty) {
      return 'Device name is required';
    }

    if (deviceName.trim().length < 3) {
      return 'Device name must be at least 3 characters';
    }

    if (location.trim().isEmpty) {
      return 'Location is required';
    }

    if (location.trim().length < 3) {
      return 'Location must be at least 3 characters';
    }

    return null;
  }

  /// Reset registration state
  void resetState() {
    _status = DeviceRegistrationStatus.idle;
    _currentDeviceId = null;
    _errorMessage = null;
    _progress = 0.0;
    notifyListeners();
  }

  void _setStatus(DeviceRegistrationStatus status) {
    _status = status;
    notifyListeners();
  }

  void _setProgress(double progress) {
    _progress = progress;
    notifyListeners();
  }
}
